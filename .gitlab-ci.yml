stages:
    - quality
    - prepare
    - test
    - trigger

default:
    tags:
        - anetdev

workflow:
    rules:
        - if: '$CI_COMMIT_BRANCH == "preprod-socle"'
          variables: { ENV_TIER: "preprod" }
        - if: '$CI_COMMIT_BRANCH == "main-socle"'
          variables: { ENV_TIER: "prod" }
        - when: always
          variables: { ENV_TIER: "dev" }
          
# ---- Étape 1 — Jobs globaux (non spécifiques à une enseigne) ----

php-cs-fixer:
    stage: quality
    image: gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:dev
    before_script: 
        - ENSEIGNE="normandiepharma" #juste pour installation
    script:
        - PHP_CS_FIXER_IGNORE_ENV=1 ./vendor/bin/php-cs-fixer fix --dry-run --diff
    allow_failure: true

security-checker:
    stage: quality
    image: jakzal/phpqa:php8.4
    needs: []
    allow_failure: false
    script:
        - local-php-security-checker --path=./composer.lock --format=junit > local-php-security-checker.xml
    artifacts:
        when: always
        paths:
            - local-php-security-checker.xml
        reports:
            junit: local-php-security-checker.xml
        expire_in: 30 minutes

# ---- Étape 2 — Génère le fichier matrix.yml basé sur les fichiers changés ----

generate-matrix:
    stage: prepare
    script:
        - apk add --no-cache bash
        - chmod +x .gitlab/scripts/generate-matrix.sh
        - mkdir -p .gitlab/generated/
        - apk update && apk add curl jq
        - bash .gitlab/scripts/generate-matrix.sh
    artifacts:
        paths:
            - .gitlab/generated/

install-socle:
    stage: prepare
    image: gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:dev
    script:
        - echo "Installing dependencies for socle"
        - sudo -HEu www-data composer install --no-interaction --prefer-dist --no-progress --no-scripts
        # retourner une valeur pour stoper le job si fail
    artifacts:
        paths:
            - vendor
            - public/build
        expire_in: 10 minutes
        
# ---- Étape 3 — Test  ----

# Tests globaux (socle) avant de lancer la sous-pipeline
test_socle:
    stage: test
    image: gitlab.alienor.net:5050/dev-projets-aquitem/zefid_portail_client:dev
    needs: ["install-socle"]
    services:
        - name: wiremock/wiremock:latest
          alias: wiremock
    script:
        - echo "Running global tests (socle)"
        - sudo -HEu www-data php bin/console importmap:install
        - sudo -HEu www-data php bin/console wiremock:start
        - sudo -HEu www-data php -d memory_limit=512M vendor/bin/phpunit \
            -c phpunit.xml.dist \
            --log-junit global.xml
    artifacts:
        when: always
        reports:
            junit:
                - global.xml
        paths:
            - tests/approvals/*.received.html
            - tests/approvals/*.approved.html
        expire_in: 30 minutes

# ---- Étape 4 — Lancer le pipeline enfant avec la matrice générée ----
        
# Fallback si aucune enseigne n'est modifiée
empty-enseigne:
    stage: trigger
    needs: ["generate-matrix"]
    script:
        - echo "Nothing to do (no enseigne changes detected)"
        - ls .gitlab/generated/
    rules:
        - exists:
              - .gitlab/generated/ci_without_modification_on_enseigne.yml
          when: always # S’exécute uniquement si le fichier existe
        - when: never # Sinon ne s’exécute pas 
          
# Déclenchement de la sous-pipeline dynamique des enseignes si test_socle n'échoue pas
trigger-dynamic-child:
    stage: trigger
    needs: [generate-matrix, test_socle]
    trigger:
        include:
            - artifact: .gitlab/generated/gitlab-ci.generated.yml
              job: generate-matrix
        strategy: depend
        forward:
            yaml_variables: true
            pipeline_variables: true
    rules:
        - exists:
              - .gitlab/generated/ci_without_modification_on_enseigne.yml
          when: never  # Si le fichier ci_empty.yml existe, la pipeline ne s'exécute pas
        - when: always  # Sinon, la pipeline continue normalement

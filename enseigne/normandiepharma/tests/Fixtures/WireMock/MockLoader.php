<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Tests\Fixtures\WireMock;

use WireMock\Client\WireMock;

class MockLoader
{
    public const string CLIENT_1_CODE_CARTE = '2902902902940';
    public const string CLIENT_1_PASSWORD = '16/07/1987';
    public const string CLIENT_1_PASSWORD_FAKE = '16/07/1986';

    // client-select-with-anniversaire-expire.xml
    public const string CLIENT_CODE_CARTE_WITH_ANNIVERSAIRE_EXPIRE = '2902902906924';
    public const string CLIENT_PASSWORD_WITH_ANNIVERSAIRE_EXPIRE = '23/03/2000';

    // client-select-with-anniversaire-valide.xml
    public const string CLIENT_CODE_CARTE_WITH_ANNIVERSAIRE_VALIDE = '2902902906917';
    public const string CLIENT_PASSWORD_WITH_ANNIVERSAIRE_VALIDE = '24/04/2000';

    // client-select-with-bienvenue-expire.xml
    public const string CLIENT_CODE_CARTE_WITH_BIENVENUE_EXPIRE = '2902902906900';
    public const string CLIENT_PASSWORD_WITH_BIENVENUE_EXPIRE = '16/07/1987';

    // client-select-with-bienvenue-valide.xml
    public const string CLIENT_CODE_CARTE_WITH_BIENVENUE_VALIDE = '2902902906931';
    public const string CLIENT_PASSWORD_WITH_BIENVENUE_VALIDE = '23/03/2000';

    // client-select-with-cheque-expire.xml
    public const string CLIENT_CODE_CARTE_WITH_CHEQUE_EXPIRE = '2902902902933';
    public const string CLIENT_PASSWORD_WITH_CHEQUE_EXPIRE = '01/02/2000';

    // client-select-with-cheque-valide.xml
    public const string CLIENT_CODE_CARTE_WITH_CHEQUE_VALIDE = '2902902902926';
    public const string CLIENT_PASSWORD_WITH_CHEQUE_VALIDE = '16/07/1987';

    // client-select-without-cheque.xml
    public const string CLIENT_CODE_CARTE_WITHOUT_CHEQUE = '2902902901004';
    public const string CLIENT_PASSWORD_WITHOUT_CHEQUE = '23/06/2000';

    // code-carte-convert-successful.xml
    public const string CLIENT_CODE_CARTE_ALPHANUMERIC = '2F04D030B60S4';
    public const string CLIENT_CODE_CARTE_TOKEN = '4cd2ad8a05';

    // code-carte-convert-failure.xml
    public const string CLIENT_CODE_CARTE_WRONG = 'BADCODE123';
    public const string CLIENT_CODE_CARTE_WRONG_TOKEN = 'wrongLongToken';

    // client-update-successful.xml
    public const string CLIENT_PHONE_NUM = '0606060606';
    public const string CLIENT_DATE_NAISSANCE = '25/10/1949';

    public static function configureMocks(WireMock $wireMock, string $projectDir): void
    {
        $programme = $_ENV['PROGRAMME_AQUITEM_KEY'] ?? $_SERVER['PROGRAMME_AQUITEM_KEY'] ?? 'normandiepharmafakekey';
        $enseigneDir = $projectDir.'/enseigne/'.$_ENV['ENSEIGNE'];
        $wireMock->stubFor(WireMock::get(WireMock::urlPathEqualTo('/PhDClientStaticTablesSelect.php'))
            ->withQueryParam('programme', WireMock::equalTo($programme))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/tablestatic-select-successful.xml'))));

        self::addClientWithoutChequeEndpoint($wireMock, $enseigneDir);
        self::addClientWithChequeEndpoint($wireMock, $enseigneDir);
        self::addFailConnectionClientEndpoint($wireMock, $enseigneDir);

        self::addClientCodeCarteWithAnniversaireExpireEndpoint($wireMock, $enseigneDir);
        self::addClientCodeCarteWithAnniversaireValideEndpoint($wireMock, $enseigneDir);
        self::addClientCodeCarteWithBienvenueExpireEndpoint($wireMock, $enseigneDir);
        self::addClientCodeCarteWithBienvenueValideEndpoint($wireMock, $enseigneDir);
        self::addClientCodeCarteWithChequeExpireEndpoint($wireMock, $enseigneDir);
        self::addClientCodeCarteWithChequeValideEndpoint($wireMock, $enseigneDir);
        self::addClientCodeCarteWithoutChequeEndpoint($wireMock, $enseigneDir);
        // Stubs pour le WS de conversion alphanum -> numérique
        self::addFailConversionWithWrongCodeCarte($wireMock, $enseigneDir);
        self::addSuccessConversionCodeCarte($wireMock, $enseigneDir);

        // Todo: Realiser le mocking pour le success et la failure de update client
//        $wireMock->stubFor(
//            WireMock::post(WireMock::urlPathEqualTo('/PhDClientClientUpdate.php'))
//                ->withQueryParam('Mobile', WireMock::equalTo(self::CLIENT_PHONE_NUM))
//                ->withQueryParam('DateNaissance', WireMock::equalTo(self::CLIENT_DATE_NAISSANCE))
//                ->withQueryParam('programme', WireMock::equalTo('normandiepharmafakekey'))
//                ->willReturn(
//                    WireMock::aResponse()
//                        ->withHeader('Content-Type', 'text/plain')
//                        ->withBody(
//                            file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/client-update-successful.xml')
//                        )
//                )
//                ->atPriority(1)
//        );

        $wireMock->stubFor(WireMock::post(WireMock::urlPathEqualTo('/PhDClientClientUpdate.php'))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/client-update-failure.xml'))
            )
            ->atPriority(10)
        );
    }

    private static function addFailConnectionClientEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_1_CODE_CARTE, self::CLIENT_1_PASSWORD_FAKE, 'client-select-failure-invalid-credentials.xml');
    }

    private static function addClientWithoutChequeEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_1_CODE_CARTE, self::CLIENT_1_PASSWORD, 'client-select-without-cheque-successful.xml');
    }

    private static function addClientWithChequeEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_1_CODE_CARTE, self::CLIENT_1_PASSWORD, 'client-select-with-cheque-successful.xml');
    }

    private static function addClientCodeCarteWithAnniversaireExpireEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_CODE_CARTE_WITH_ANNIVERSAIRE_EXPIRE, self::CLIENT_PASSWORD_WITH_ANNIVERSAIRE_EXPIRE, 'client-select-with-anniversaire-expire.xml');
    }

    private static function addClientCodeCarteWithAnniversaireValideEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_CODE_CARTE_WITH_ANNIVERSAIRE_VALIDE, self::CLIENT_PASSWORD_WITH_ANNIVERSAIRE_VALIDE, 'client-select-with-anniversaire-valide.xml');
    }

    private static function addClientCodeCarteWithBienvenueExpireEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_CODE_CARTE_WITH_BIENVENUE_EXPIRE, self::CLIENT_PASSWORD_WITH_BIENVENUE_EXPIRE, 'client-select-with-bienvenue-expire.xml');
    }

    private static function addClientCodeCarteWithBienvenueValideEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_CODE_CARTE_WITH_BIENVENUE_VALIDE, self::CLIENT_PASSWORD_WITH_BIENVENUE_VALIDE, 'client-select-with-bienvenue-valide.xml');
    }

    private static function addClientCodeCarteWithChequeExpireEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_CODE_CARTE_WITH_CHEQUE_EXPIRE, self::CLIENT_PASSWORD_WITH_CHEQUE_EXPIRE, 'client-select-with-cheque-expire.xml');
    }

    private static function addClientCodeCarteWithChequeValideEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_CODE_CARTE_WITH_CHEQUE_VALIDE, self::CLIENT_PASSWORD_WITH_CHEQUE_VALIDE, 'client-select-with-cheque-valide.xml');
    }

    private static function addClientCodeCarteWithoutChequeEndpoint(WireMock $wireMock, string $enseigneDir): void
    {
        self::addClientEndpoint($wireMock, $enseigneDir, self::CLIENT_CODE_CARTE_WITHOUT_CHEQUE, self::CLIENT_PASSWORD_WITHOUT_CHEQUE, 'client-select-without-cheque.xml');
    }

    private static function addFailConversionWithWrongCodeCarte(WireMock $wireMock, string $enseigneDir): void
    {
        self::addConvertEndpoint(
            $wireMock,
            $enseigneDir,
            self::CLIENT_CODE_CARTE_WRONG,
            self::CLIENT_CODE_CARTE_WRONG_TOKEN,
            'code-carte-convert-failure.xml'
        );
    }

    private static function addSuccessConversionCodeCarte(WireMock $wireMock, string $enseigneDir): void
    {
        self::addConvertEndpoint(
            $wireMock,
            $enseigneDir,
            self::CLIENT_CODE_CARTE_ALPHANUMERIC,
            self::CLIENT_CODE_CARTE_TOKEN,
            'code-carte-convert-successful.xml'
        );
    }

    private static function addClientEndpoint(WireMock $wireMock, string $enseigneDir, string $codeCarte, string $dateNaissance, string $fileResponse): void
    {
        $programme = $_ENV['PROGRAMME_AQUITEM_KEY'] ?? $_SERVER['PROGRAMME_AQUITEM_KEY'] ?? 'normandiepharmafakekey';
        $wireMock->stubFor(WireMock::get(WireMock::urlPathEqualTo('/PhDClientClientSelect.php'))
            ->withQueryParam('CodeCarte', WireMock::equalTo($codeCarte))
            ->withQueryParam('DateNaissance', WireMock::equalTo($dateNaissance))
            ->withQueryParam('programme', WireMock::equalTo($programme))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/plain')
                ->withBody(file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/'.$fileResponse))));
    }

    private static function addConvertEndpoint(WireMock $wireMock, string $enseigneDir, string $codeCarte, string $token, string $fixture): void
    {
        $programme = $_ENV['PROGRAMME_AQUITEM_KEY'] ?? $_SERVER['PROGRAMME_AQUITEM_KEY'] ?? 'normandiepharmafakekey';

        $wireMock->stubFor(WireMock::get(WireMock::urlPathEqualTo('/PhDCartesAlphaSelect.php'))
            ->withQueryParam('PROGRAMME', WireMock::equalTo($programme))
            ->withQueryParam('MAGASIN', WireMock::equalTo('999999999'))
            ->withQueryParam('ANCIENCODECARTE', WireMock::equalTo($codeCarte))
            ->withQueryParam('TOKEN', WireMock::matching('[0-9a-f]{10}'))
            ->willReturn(WireMock::aResponse()
                ->withHeader('Content-Type', 'text/xml')
                ->withBody(file_get_contents($enseigneDir.'/tests/Fixtures/WireMock/'.$fixture))
            )
        );
    }
}

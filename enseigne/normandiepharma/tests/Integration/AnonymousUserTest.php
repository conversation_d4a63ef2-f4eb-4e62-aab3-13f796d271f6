<?php

namespace <PERSON><PERSON><PERSON>rma\Tests\Integration;

use App\Tests\ApiTestCase;
use ChqThomas\ApprovalTests\Approvals;
use Gajus\Dindent\Indenter;
use Normandiepharma\Tests\Fixtures\WireMock\MockLoader;
use WireMock\Client\WireMock;

class AnonymousUserTest extends ApiTestCase
{
    private $client;
    private $wireMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();
        $this->wireMock = WireMock::create('wiremock', 8080);
        $this->wireMock->reset();
        MockLoader::configureMocks(
            $this->wireMock,
            self::getContainer()->getParameter('kernel.project_dir')
        );
    }

    public function test_index(): void
    {
        $crawler = $this->client->request('GET', '/');
        $reindentedBlock = $this->indenter->indent($crawler->outerHtml());
        $reindentedBlock = $this->scrubTokens($reindentedBlock);
        $reindentedBlock = $this->scrubCaptcha($reindentedBlock);
        $reindentedBlock = $this->scrubAssetMapperLinks($reindentedBlock);

        Approvals::verifyHtml($reindentedBlock);
    }

    public function test_login_success(): void
    {
        $crawler = $this->client->request('GET', '/');
        $this->client->submitForm('login_submit', [
            'login[_username]' => MockLoader::CLIENT_1_CODE_CARTE,
            'login[_password]' => MockLoader::CLIENT_1_PASSWORD,
            'login[_token]' => $crawler->filter('input[name="login[_token]"]')->attr('value'),
        ]);
        $this->client->followRedirect();

        self::assertResponseIsSuccessful();
        self::assertRouteSame('home_vue');
    }

    public function test_login_failed(): void
    {
        $crawler = $this->client->request('GET', '/');
        $this->client->submitForm('login_submit', [
            'login[_username]' => MockLoader::CLIENT_1_CODE_CARTE,
            'login[_password]' => MockLoader::CLIENT_1_PASSWORD_FAKE,
            'login[_token]' => $crawler->filter('input[name="login[_token]"]')->attr('value'),
        ]);
        $this->client->followRedirect();

        self::assertResponseStatusCodeSame(401);
        self::assertRouteSame('home_login_statut');
    }
}

<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Tests\Integration;

use App\Services\NewClientsService;
use App\Tests\ApiTestCase;
use ChqThomas\ApprovalTests\Approvals;
use Normandiepharma\Tests\Fixtures\WireMock\MockLoader;
use <PERSON>ymfony\Component\DomCrawler\Crawler;
use WireMock\Client\WireMock;

class VueChequeTest extends ApiTestCase
{
    private $client;
    private WireMock $wireMock;

    public function getVue(): ?Crawler
    {
        return $this->client->request('GET', '/vue/');
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->client = self::createClient();
        $this->wireMock = WireMock::create('wiremock', 8080);
        $this->wireMock->reset();
        MockLoader::configureMocks($this->wireMock, self::getContainer()->getParameter('kernel.project_dir'));
    }
    private function loginUser($codeCarte, $password): void
    {
        $user = self::getContainer()->get(NewClientsService::class)->authenticate($codeCarte, $password);
        $this->client->loginUser($user, 'secured_area');
    }

    public function clientProvider(): iterable
    {
        yield [MockLoader::CLIENT_CODE_CARTE_WITH_CHEQUE_EXPIRE, MockLoader::CLIENT_PASSWORD_WITH_CHEQUE_EXPIRE];
        yield [MockLoader::CLIENT_CODE_CARTE_WITH_CHEQUE_VALIDE, MockLoader::CLIENT_PASSWORD_WITH_CHEQUE_VALIDE];
        yield [MockLoader::CLIENT_CODE_CARTE_WITHOUT_CHEQUE, MockLoader::CLIENT_PASSWORD_WITHOUT_CHEQUE];
        yield [MockLoader::CLIENT_CODE_CARTE_WITH_ANNIVERSAIRE_EXPIRE, MockLoader::CLIENT_PASSWORD_WITH_ANNIVERSAIRE_EXPIRE];
        yield [MockLoader::CLIENT_CODE_CARTE_WITH_ANNIVERSAIRE_VALIDE, MockLoader::CLIENT_PASSWORD_WITH_ANNIVERSAIRE_VALIDE];
        yield [MockLoader::CLIENT_CODE_CARTE_WITH_BIENVENUE_EXPIRE, MockLoader::CLIENT_PASSWORD_WITH_BIENVENUE_EXPIRE];
        yield [MockLoader::CLIENT_CODE_CARTE_WITH_BIENVENUE_VALIDE, MockLoader::CLIENT_PASSWORD_WITH_BIENVENUE_VALIDE];
    }

    /**
     * @dataProvider clientProvider
     */
    public function test_client_mes_avantages($codeCarte, $password): void
    {
        $this->loginUser($codeCarte, $password);
        $crawler = $this->getVue();
        $reindentedBlock = $this->getHtmlBlock($crawler, 'mesAvantages');

        Approvals::verifyHtml($reindentedBlock);
    }
}

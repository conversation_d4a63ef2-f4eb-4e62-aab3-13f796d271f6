<?php

namespace Normandiepharma\Tests\Unit;

use App\DTO\WebserviceError;
use App\Serializer\CodeClientDeserializer;
use App\DTO\WebserviceResponse;
use App\Tests\ApiTestCase;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Serializer\Encoder\XmlEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

class CodeClientDeserializerTest extends ApiTestCase
{
    private CodeClientDeserializer $deserializer;

    protected function setUp(): void
    {
        $serializer = new Serializer([new ObjectNormalizer()], [new XmlEncoder()]);
        $this->deserializer = new CodeClientDeserializer($serializer);
    }

    public function testSuccessfulConversion(): void
    {
        $fixturePath = __DIR__ . '/../Fixtures/WireMock/code-carte-convert-successful.xml';
        $xml = file_get_contents($fixturePath);

        $result = $this->deserializer->deserialize($xml);

        $this->assertIsString($result);
        $this->assertSame('2902902906900', $result);
    }

    public function testConversionError(): void
    {
        $fixturePath = __DIR__ . '/../Fixtures/WireMock/code-carte-convert-failure.xml';
        $xml = file_get_contents($fixturePath);

        $result = $this->deserializer->deserialize($xml);

        $this->assertInstanceOf(WebserviceError::class, $result);
    }
}

<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Tests\Unit;

use App\Serializer\ResponseDeserializerInterface;
use Normandiepharma\Services\CodeClientConverter;
use App\DTO\WebserviceError;
use App\Serializer\CodeClientDeserializer;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;


class CodeClientConverterTest extends TestCase
{
    private HttpClientInterface $httpClient;
    private ResponseDeserializerInterface $deserializer;
    private ParameterBagInterface $parameterBag;
    protected function setUp(): void
    {
        $this->httpClient = $this->createMock(HttpClientInterface::class);
        $this->parameterBag = $this->createMock(ParameterBagInterface::class);
        $this->deserializer = $this->createMock(ResponseDeserializerInterface::class);
        $this->codeConverterParams = [
            'PROGRAMME' => 'fakeProgram',
            'MAGASIN' => '123',
            'salt_client' => 'fakeSalt',
        ];
    }

    public function testThrowsWhenNotAlphanumeric(): void
    {
        $this->expectException(CustomUserMessageAuthenticationException::class);

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );
        $converter->convert('BAD-CODE');
    }

    public function testReturnsPlainWhenNumeric(): void
    {
        $numericCode = '123456';
        // HttpClient should not be called
        $this->httpClient->expects($this->never())->method('request');

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );

        $this->assertSame($numericCode, $converter->convert($numericCode));
    }

    public function testSuccessfulConversion(): void
    {
        $codeCarte = '2F04D030B60S4';

        $expectedToken = substr(
            md5(sprintf(
                'PROGRAMME=%s&MAGASIN=%s&ANCIENCODECARTE=%smdp=%s',
                'fakeProgram',
                '123',
                $codeCarte,
                'fakeSalt'
            )),
            9,
            10
        );

        $responseMock = $this->createMock(ResponseInterface::class);
        $responseMock
            ->method('getContent')
            ->willReturn('<irrelevant/>');


        $this->httpClient
            ->expects($this->once())
            ->method('request')
            ->with(
                'GET',
                CodeClientConverter::CODECARTE_CONVERT_ROUTE,
                ['query' => [
                    'PROGRAMME'       => 'fakeProgram',
                    'MAGASIN'         => '123',
                    'ANCIENCODECARTE' => $codeCarte,
                    'TOKEN'           => $expectedToken,
                ]]
            )
            ->willReturn($responseMock);

        $this->deserializer
            ->expects($this->once())
            ->method('deserialize')
            ->with('<irrelevant/>')
            ->willReturn('convertedCode');

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );

        $this->assertSame('convertedCode', $converter->convert($codeCarte));
    }

    public function testConversionErrorLoadsFixture(): void
    {
        $codeCarte = 'BADCODE123';
        $responseMock = $this->createMock(ResponseInterface::class);
        $responseMock
            ->method('getContent')
            ->willReturn('<irrelevant/>');

        $this->httpClient
            ->method('request')
            ->willReturn($responseMock);

        $errorMock = $this->createMock(WebserviceError::class);
        $this->deserializer
            ->method('deserialize')
            ->willReturn($errorMock);

        $converter = new CodeClientConverter(
            $this->httpClient,
            $this->deserializer,
            $this->codeConverterParams
        );

        $result = $converter->convert($codeCarte);
        $this->assertSame($errorMock, $result);
    }
}

<?php

declare(strict_types=1);

use App\Serializer\ArobaseUppercaseToLowercaseNameConverter;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Config\FrameworkConfig;

return static function (FrameworkConfig $framework, ContainerConfigurator $containerConfigurator): void {
    $paths = [];

    $enseigne = $_ENV['ENSEIGNE'] ?? null;
    if ($enseigne) {
        $pathSerializerDirEnseigne = sprintf(__DIR__.'/../../enseigne/%s/config/serializer', $_ENV['ENSEIGNE']);
        if (is_dir($pathSerializerDirEnseigne)) {
            $paths[] = $pathSerializerDirEnseigne;
        }
    }

    $framework
        ->secret('%env(APP_SECRET)%')
        ->httpMethodOverride(false)
        ->handleAllThrowables(true)
        ->ide('%env(IDE_CONFIG)%');

    $framework
        ->form()
        ->csrfProtection()
        ->tokenId('submit')
    ;

    $framework
        ->csrfProtection()
        ->statelessTokenIds([
            'submit',
            'authenticate',
            'logout',
        ])
    ;

    $framework->session()->enabled(true);

    $framework->serializer()
        ->nameConverter(ArobaseUppercaseToLowercaseNameConverter::class)
        ->defaultContext([
            DateTimeNormalizer::FORMAT_KEY => 'd/m/Y',
            AbstractObjectNormalizer::DISABLE_TYPE_ENFORCEMENT => true,
        ])
        ->mapping()
            ->paths($paths);


    if ('test' === $containerConfigurator->env()) {
        $framework
            ->test(true)
            ->session()
            ->storageFactoryId('session.storage.factory.mock_file');
    }
};

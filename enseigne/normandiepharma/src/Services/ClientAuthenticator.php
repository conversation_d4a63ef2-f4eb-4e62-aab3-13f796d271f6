<?php

namespace Normandiepharma\Services;

use App\DTO\WebserviceError;
use App\Entity\ClientInterface;
use App\Services\ClientAuthenticatorInterface;

readonly class ClientAuthenticator implements ClientAuthenticatorInterface
{
    public function __construct(
        private ClientAuthenticatorInterface $authenticator,
        private CodeClientConverter $converter,
    ) {
    }

    public function authenticate(string $username, string $password): WebserviceError|ClientInterface|true
    {
        $converted = $this->converter->convert($username);
        if ($converted instanceof WebserviceError) {
            return $converted;
        }
        return $this->authenticator->authenticate($converted, $password);
    }
}

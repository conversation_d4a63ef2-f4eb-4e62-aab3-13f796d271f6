<?php

namespace Normandiepharma\Services;

use App\DTO\WebserviceError;
use App\Serializer\ResponseDeserializerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class CodeClientConverter implements CodeClientConverterInterface
{

    public function __construct(
        #[Autowire(service: 'ws2')] private readonly HttpClientInterface $codeConverterClient,
        private readonly ResponseDeserializerInterface $codeClientDeserializer,
        private readonly array $codeConverterParams
    )
    {
    }
    public const string CODECARTE_CONVERT_ROUTE = 'PhDCartesAlphaSelect.php';

    public function convert(string $codeCarte): WebserviceError|string
    {
        if (!ctype_alnum($codeCarte)) {
            throw new CustomUserMessageAuthenticationException('Numéro de carte invalide.');
        }
        if (ctype_digit($codeCarte)) {
            return $codeCarte;
        }

        $params = $this->codeConverterParams;
        $programme = $params['PROGRAMME'];
        $magasin = $params['MAGASIN'];
        $salt = $params['salt_client'];
        $token = $this->calculateToken($programme, $magasin, $codeCarte, $salt);

        $response = $this->codeConverterClient->request(
            'GET',
            $this->getCodeCarteConvertRoute(),
            ['query' => [
                'PROGRAMME' => $programme,
                'MAGASIN' => $magasin,
                'ANCIENCODECARTE' => $codeCarte,
                'TOKEN' => $token,
            ]]
        )->getContent();

        return $this->codeClientDeserializer->deserialize($response);
    }
    protected function calculateToken(string $programme, string $magasin, string $codeCarte, string $salt): string
    {
        $plain = sprintf(
            'PROGRAMME=%s&MAGASIN=%s&ANCIENCODECARTE=%smdp=%s',
            $programme,
            $magasin,
            $codeCarte,
            $salt
        );

        return substr(md5($plain), 9, 10);
    }

    protected function getCodeCarteConvertRoute(): string
    {
        return self::CODECARTE_CONVERT_ROUTE;
    }
}

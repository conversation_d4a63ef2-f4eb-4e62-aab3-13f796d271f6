FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_base:8.4-frankenphp AS frankenphp_base
FROM gitlab.alienor.net:5050/dev-docker/web_php/frankenphp_dev:8.4-frankenphp AS frankenphp_dev

ARG APACHE_RUN_USER=www-data
ARG APACHE_RUN_GROUP=www-data
ARG UID_USER=1001
ARG GID_USER=1001

RUN usermod -u ${UID_USER} ${APACHE_RUN_USER} && groupmod -g ${GID_USER} ${APACHE_RUN_GROUP}

RUN mkdir -p /var/www/.composer/cache && chown -R www-data:www-data /var/www/.composer
RUN apt-get update && apt-get install -y ssh

# ~~~~~~ GITLAB KEY ~~~~~~

RUN mkdir -p /root/.ssh/ && \
mkdir -p /var/www/.ssh/ && \
curl https://www.groupe-aquitem.fr/keys/intern.key -o /root/.ssh/id_rsa && \
chmod 600 /root/.ssh/id_rsa && \
cp /root/.ssh/id_rsa /var/www/.ssh/id_rsa && \
chmod 600 /var/www/.ssh/id_rsa  && \
chown www-data:www-data /var/www/.ssh/id_rsa

RUN ssh-keyscan -t rsa gitlab.alienor.net > /var/www/.ssh/known_hosts && chown www-data:www-data /var/www/.ssh/known_hosts



#FROM frankenphp_base AS frankenphp_build_prod
FROM frankenphp_base AS frankenphp_prod
ARG ENSEIGNE
ARG ENV_TIER=prod
ARG APP_ENV=prod

ENV ENSEIGNE=${ENSEIGNE}
ENV ENV_TIER=${ENV_TIER}
ENV APP_ENV=${APP_ENV}

ENV FRANKENPHP_CONFIG="import worker.Caddyfile"

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY --link frankenphp/conf.d/20-app.prod.ini $PHP_INI_DIR/conf.d/
COPY --link frankenphp/worker.Caddyfile /etc/caddy/worker.Caddyfile

# ~~~~~~ GITLAB KEY ~~~~~~

RUN mkdir /root/.ssh/ && \
mkdir /var/www/.ssh/ && \
curl https://www.groupe-aquitem.fr/keys/intern.key -o /root/.ssh/id_rsa && \
chmod 600 /root/.ssh/id_rsa && \
cp /root/.ssh/id_rsa /var/www/.ssh/id_rsa && \
chmod 600 /var/www/.ssh/id_rsa  && \
chown www-data:www-data /var/www/.ssh/id_rsa

RUN apt-get update && apt-get install -y ssh
RUN ssh-keyscan -t rsa gitlab.alienor.net > /var/www/.ssh/known_hosts && chown www-data:www-data /var/www/.ssh/known_hosts

# prevent the reinstallation of vendors at every changes in the source code
COPY --link composer.* symfony.* ./
RUN set -eux; \
    sudo -Eu www-data composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress

# copy sources
COPY --link . ./
RUN rm -Rf frankenphp/

RUN set -eux; \
    sudo -Eu www-data mkdir -p var/cache var/log; \
    sudo -Eu www-data composer dump-autoload --classmap-authoritative --no-dev; \
    sudo -Eu www-data composer run-script --no-dev post-install-cmd; \
    chmod +x bin/console; sync;

RUN set -eux; \
    echo 'nettoyage des autres enseignes' \
    find enseigne/* -maxdepth 0 -not -name $ENSEIGNE -exec rm -rf {} + \
    find public/enseigne/* -maxdepth 0 -not -name $ENSEIGNE -exec rm -rf {} +;

RUN php bin/console asset-map:compile

RUN chown -R root:root . && chmod -R 555 . \
    && chown -R www-data:www-data var/cache && chmod -R 775 var/cache \
    && chown -R www-data:www-data var/log && chmod -R 775 var/log

COPY --link --chmod=755 frankenphp/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
ENTRYPOINT ["docker-entrypoint"]
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile" ]


#FROM frankenphp_build_prod AS frankenphp_prod

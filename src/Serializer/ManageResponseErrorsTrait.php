<?php
namespace App\Serializer;

use App\DTO\WebserviceError;
use App\DTO\WebserviceResponse;
use App\Services\ErrorManagementService;

trait ManageResponseErrorsTrait
{
    protected function manageResponseErrors(WebserviceResponse $response): ?WebserviceError
    {
        if ($response->exception) {
            $attributes = $response->exception;
            if (isset($attributes['@finalExceptionCode']) && 0 != $attributes['@finalExceptionCode']) {
                return new WebserviceError($attributes['@finalExceptionCode']);
            }

            return $this->manageException($response->exception);
        }
        if ($response->code) {
            return new WebserviceError($response->code);
        }

        return null;
    }

    protected function manageException(array $exception): WebserviceError
    {
        if (isset($exception['sqlerrm']['exception']) && is_array($exception['sqlerrm']['exception'])) {
            return $this->manageException($exception['sqlerrm']['exception']);
        }

        if ($exception['@sqlcode']) {
            return new WebserviceError($exception['@sqlcode']);
        }

        return new WebserviceError(ErrorManagementService::C_ERR_CASCADE);
    }
}

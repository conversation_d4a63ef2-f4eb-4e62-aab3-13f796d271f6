<?php

declare(strict_types=1);

use App\Form\ClientType;
use App\Form\ClientTypeInterface;
use App\Form\Login\LoginType;
use App\Form\Login\LoginTypeInterface;
use App\Form\Login\LoginWithoutCaptchaType;
use App\Security\AuthenticatorWithoutCaptcha;
use App\Security\HCaptchaAuthenticator;
use App\Serializer\CollectionNormalizer;
use App\Services\Cagnotte\CagnotteFormaterStrategies\CagnotteFormaterStrategyInterface;
use App\Services\Cagnotte\CagnotteFormaterStrategies\CagnotteIntegerFormaterStrategy;
use App\Services\Cagnotte\CagnotteStrategies\CagnotteStrategyInterface;
use App\Services\Cagnotte\CagnotteStrategies\CagnotteWithThreshold;
use App\Services\Cagnotte\CagnotteTypes\CagnotteTypeStrategyInterface;
use App\Services\Cagnotte\CagnotteTypes\PointCagnotte;
use App\Services\Cheque\ChequeSecurityFactory;
use App\Services\Cheque\ChequeSecurityFactoryInterface;
use App\Services\DefaultCodeClientConverter;
use App\Services\DotEnvEnseigneLoader;
use App\Services\FeatureFlipping\FeatureCheckerInterface;
use App\Services\FeatureFlipping\InMemoryFeatureChecker;
use App\Services\GlobalDefinitionsServiceInterface;
use App\Services\NewClientsService;
use App\Services\NewGlobalDefinitionsService;
use App\Services\NullWsLogger;
use App\Services\PDFCheque\ChequeDtoFactory;
use App\Services\PDFCheque\ChequeDtoFactoryInterface;
use App\Services\Wallet\WalletInterface;
use App\Services\Wallet\ZefidWallet;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use function Symfony\Component\DependencyInjection\Loader\Configurator\param;
use function Symfony\Component\DependencyInjection\Loader\Configurator\service;

return static function (ContainerConfigurator $containerConfigurator): void {
    $enseigne = $_ENV['ENSEIGNE'] ?? null;

    if ($enseigne) {
        $pathParametersClientEnseigne = sprintf(__DIR__ . '/../enseigne/%s/config/parametersClient.yml', $_ENV['ENSEIGNE']);
        if (is_file($pathParametersClientEnseigne)) {
            $containerConfigurator->import($pathParametersClientEnseigne, 'yml');
        } else {
            $containerConfigurator->import(__DIR__ . '/parametersClient.yml', 'yml');
        }
    } else {
        $containerConfigurator->import(__DIR__ . '/parametersClient.yml', 'yml');
    }
    $parameters = $containerConfigurator->parameters();
    $parameters->set('programme', '%env(string:PROGRAMME_AQUITEM_KEY)%');

    $parameters->set('kernel.config_dir', '%kernel.project_dir%/config/');

    $parameters->set('EnseigneId', '%env(string:ENSEIGNE)%');

    $parameters->set('enseigne', '%env(string:ENSEIGNE)%');

    $parameters->set('aquitem.doncheque.token.alienor', '%env(AQUITEM_DONCHEQUE_TOKEN_ALIENOR)%');

    $parameters->set('aquitem.doncheque.token.aquitem', '%env(AQUITEM_DONCHEQUE_TOKEN_AQUITEM)%');

    $parameters->set('features', [
        'activation' => true,
        'don' => true,

        // BIENVENUE
        'remise.bienvenue' => true,
        'remise.bienvenue.telechargement' => true,

        // ANNIVERSAIRE
        'remise.anniversaire' => true,
        'remise.anniversaire.telechargement' => true,

        // FIDELITE
        'remise.fidelite' => true,
        'remise.fidelite.telechargement' => true,
    ]);

    $parameters->set('webservice_static_params', [
        'programme' => ['key' => 'programme', 'value' => '%env(string:PROGRAMME_AQUITEM_KEY)%'],
    ])
    ->set('map', true);

    $services = $containerConfigurator->services();

    $services->defaults()
        ->autowire()
        ->autoconfigure()
        ->bind('$projectDir', '%kernel.project_dir%')
        ->bind('$webserviceStaticParams', param('webservice_static_params'))
        ->bind('$wsLogger', service('alienor.wslogger'));

    $services->load('App\\', __DIR__ . '/../src/')
        ->exclude([
            __DIR__ . '/../src/DependencyInjection/',
            __DIR__ . '/../src/Entity/',
            __DIR__ . '/../src/Kernel.php',
            __DIR__ . '/../src/Tests/',
        ]);

    $services->load('App\Controller\\', __DIR__ . '/../src/Controller/')
        ->tag('controller.service_arguments');

    // Params pour le logger-webservice-bundle
    $isProd = 'prod' === $containerConfigurator->env();
    $parameters->set('webservice.prod', $isProd);
    if (!$isProd) {
        $services->load('Alienor\LoggerWebserviceBundle\\', __DIR__ . '/../vendor/alienor/logger-webservice-bundle/*')
            ->exclude([
                __DIR__ . '/../vendor/alienor/logger-webservice-bundle/{Entity,Repository,Tests}',
            ]);
    }

    $services->set(InMemoryFeatureChecker::class)
        ->args([
            '%features%',
        ]);

    $services->alias(FeatureCheckerInterface::class, InMemoryFeatureChecker::class);
    $services->alias(WalletInterface::class, ZefidWallet::class);
    $services->alias(CagnotteStrategyInterface::class, CagnotteWithThreshold::class);
    $services->alias(CagnotteTypeStrategyInterface::class, PointCagnotte::class);
    $services->alias(CagnotteFormaterStrategyInterface::class, CagnotteIntegerFormaterStrategy::class)->public();
    $services->alias(GlobalDefinitionsServiceInterface::class, NewGlobalDefinitionsService::class);
    $services->alias(ChequeSecurityFactoryInterface::class, ChequeSecurityFactory::class);
    $services->alias(ChequeDtoFactoryInterface::class, ChequeDtoFactory::class);
//    $services->alias(CodeClientConverterInterface::class, DefaultCodeClientConverter::class);
    $services->set(NewClientsService::class)
        ->public();

    $services->alias('enseigne_authenticator', HCaptchaAuthenticator::class)->public();
    $services->alias(LoginTypeInterface::class, LoginType::class)->public();
    if ('prod' !== $containerConfigurator->env()) {
        $services->alias(LoginTypeInterface::class, LoginWithoutCaptchaType::class)->public();
        $services->alias('enseigne_authenticator', AuthenticatorWithoutCaptcha::class)->public();
    }

    $services->set(DotEnvEnseigneLoader::class)
        ->tag('container.env_var_loader');

    $services->set(CollectionNormalizer::class)
        ->tag('serializer.normalizer', [
            'priority' => 20,
        ]);

    $services->alias(ClientTypeInterface::class, ClientType::class)->public();

    if ('dev' !== $containerConfigurator->env()) {
        $services->alias('alienor.wslogger', NullWsLogger::class);
    }
};
